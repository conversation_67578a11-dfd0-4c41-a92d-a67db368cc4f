/**
 * 统一API Hook系统导出
 * 提供完整的API Hook生态系统
 */

// 核心Hook导出
export { useUnifiedApi, createApiHook, useApiImmediate, useApiManual, useApiPolling, useApiWithHealthCheck, useApiCached } from './useUnifiedApi';

// 配置系统导出
export { API_PRESETS, getApiPreset, mergeApiConfig, createCustomPreset, validateApiConfig, getRecommendedConfig } from './configs/apiConfigs';

// 插件系统导出
export { createRetryPlugin, executeWithRetry } from './plugins/retryPlugin';
export { createCachePlugin, cacheManager } from './plugins/cachePlugin';

// 类型定义导出
export * from './types';

// 默认导出 - 最常用的Hook
export { useUnifiedApi as default } from './useUnifiedApi';
