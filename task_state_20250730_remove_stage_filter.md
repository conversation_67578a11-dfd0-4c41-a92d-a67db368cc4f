# 任务状态文件

## 基本信息
- **任务名称**: 移除争分夺秒排行榜API请求中的stage过滤条件
- **创建时间**: 2025-07-30T14:50:00Z
- **最后同步时间**: 2025-07-30T14:50:00Z
- **当前Mode**: REVIEW
- **执行进度**: 100%
- **质量门控状态**: PASSED

## 任务描述
移除争分夺秒排行榜API请求中的 `~and(stage,eq,通用题)` 过滤条件，简化查询逻辑。

具体需要修改的API请求：
```
where: `(session_id,eq,${sectionName})~and(question_pack_id,eq,1)~and(stage,eq,通用题)`,
```

修改后应该为：
```
where: `(session_id,eq,${sectionName})~and(question_pack_id,eq,1)`,
```

## 项目概述
基于刚才完成的字段名修改，继续优化争分夺秒排行榜的API查询条件。

---
*以下部分由AI在协议执行过程中维护*
---

## 准备摘要（PREPARATION Mode填充）
- **上下文质量得分**: 10/10
- **关键发现**: 
  - 目标文件: nexus-panel/src/services/api/rankingApi.ts (第648行)
  - 当前where条件包含stage过滤
  - 需要移除stage相关的过滤条件
  - 这是对之前修改的进一步优化
